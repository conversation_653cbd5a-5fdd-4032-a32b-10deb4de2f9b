# FX Compare - Live Exchange Rate Comparison

A comprehensive forex rate comparison website built with Next.js 14, TypeScript, and Tailwind CSS. Compare live exchange rates from multiple providers including Wise and ExchangeRate.host with server-side rendering and intelligent caching.

## 🚀 Features

- **Live Exchange Rates**: Real-time data from multiple providers
- **Server-Side Rendering**: Fast initial page loads with SEO optimization
- **Intelligent Caching**: ISR (Incremental Static Regeneration) with 60-second revalidation
- **Best Rate Highlighting**: Automatically identifies and highlights the best rates
- **Currency Converter**: Real-time conversion calculator
- **Responsive Design**: Mobile-first design with Tailwind CSS
- **Client-Side Updates**: Auto-refresh every 60 seconds with SWR
- **Error Handling**: Graceful fallbacks for API failures
- **TypeScript**: Full type safety throughout the application

## 🏗️ Tech Stack

- **Framework**: Next.js 14 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS 4
- **Data Fetching**: SWR for client-side, native fetch for server-side
- **UI Components**: Headless UI + Heroicons
- **Deployment**: Vercel-ready

## 📋 Prerequisites

- Node.js 18+
- npm, yarn, or pnpm
- Wise API token (optional, for Wise rates)

## 🛠️ Installation & Setup

### 1. Clone and Install Dependencies

```bash
git clone <repository-url>
cd fxcompare
npm install
```

### 2. Environment Configuration

Create a `.env.local` file in the root directory:

```bash
cp .env.local.example .env.local
```

Edit `.env.local` with your configuration:

```env
# Wise API Configuration (optional)
# Get your API token from https://api-docs.wise.com/
WISE_API_TOKEN=your_wise_api_token_here

# Application URL
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

**Note**: The application will work without the Wise API token, using only ExchangeRate.host data.

### 3. Start Development Server

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to view the application.

## 🌐 Usage

### Viewing Exchange Rates

Navigate to any currency pair using the format:
```
http://localhost:3000/[BASE]/[QUOTE]
```

Examples:
- `/USD/EUR` - US Dollar to Euro
- `/GBP/USD` - British Pound to US Dollar
- `/EUR/JPY` - Euro to Japanese Yen

### Supported Currencies

The application supports major currencies including:
USD, EUR, GBP, JPY, AUD, CAD, CHF, CNY, SEK, NZD, MXN, SGD, HKD, NOK, TRY, ZAR, BRL, INR, KRW, PLN, THB, IDR, HUF, CZK, ILS, CLP, PHP, AED, COP, SAR, MYR, RON, BGN, HRK, RUB, DKK, ISK

## 🏗️ Project Structure

```
src/
├── app/
│   ├── [base]/[quote]/          # Dynamic currency pair routes
│   │   ├── page.tsx             # Main comparison page
│   │   └── ClientRefresh.tsx    # Client-side refresh component
│   ├── api/rates/
│   │   └── route.ts             # API endpoint for rate fetching
│   ├── globals.css              # Global styles
│   ├── layout.tsx               # Root layout
│   └── page.tsx                 # Homepage
├── components/
│   ├── ConversionCalc.tsx       # Currency conversion calculator
│   └── RateTable.tsx            # Rate comparison table
├── data/
│   └── providers.json           # Provider metadata
├── hooks/
│   └── useRates.ts              # SWR hook for client-side fetching
├── lib/
│   ├── providers/
│   │   ├── exchangerateHost.ts  # ExchangeRate.host integration
│   │   └── wise.ts              # Wise API integration
│   └── aggregate.ts             # Rate aggregation logic
└── types/
    └── index.ts                 # TypeScript type definitions
```

## 🔧 API Integration

### Wise API
- **Endpoint**: `https://api.transferwise.com/v1/rates`
- **Authentication**: Bearer token
- **Cache**: 60 seconds
- **Rate Type**: Mid-market rates

### ExchangeRate.host
- **Endpoint**: `https://api.exchangerate.host/latest`
- **Authentication**: None required
- **Cache**: 5 minutes
- **Rate Type**: Mid-market with synthetic spread

## 🚀 Deployment

### Vercel Deployment (Recommended)

1. **Connect Repository**:
   ```bash
   # Push to GitHub/GitLab/Bitbucket
   git push origin main
   ```

2. **Deploy to Vercel**:
   - Visit [vercel.com](https://vercel.com)
   - Import your repository
   - Configure environment variables:
     - `WISE_API_TOKEN` (optional)
     - `NEXT_PUBLIC_APP_URL` (your domain)

3. **Environment Variables in Vercel**:
   - Go to Project Settings → Environment Variables
   - Add your variables for Production, Preview, and Development

### Manual Deployment

```bash
# Build the application
npm run build

# Start production server
npm start
```

### Docker Deployment

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
EXPOSE 3000
CMD ["npm", "start"]
```

## 🧪 Development

### Available Scripts

```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
```

### Adding New Providers

1. Create provider file in `src/lib/providers/`
2. Implement the provider interface
3. Add to `src/lib/aggregate.ts`
4. Update `src/data/providers.json`

Example provider structure:
```typescript
export async function fetchNewProvider(base: string, quote: string): Promise<Rate | null> {
  // Implementation
}
```

## 🔍 Monitoring & Performance

### Caching Strategy
- **Server-side**: ISR with 60-second revalidation
- **Client-side**: SWR with 60-second refresh interval
- **API responses**: 60-second cache headers

### Error Handling
- Graceful API failure handling
- Fallback to available providers
- User-friendly error messages
- Automatic retry mechanisms

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/new-feature`
3. Commit changes: `git commit -am 'Add new feature'`
4. Push to branch: `git push origin feature/new-feature`
5. Submit a Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Create an issue on GitHub
- Check the documentation
- Review the API provider documentation

## 🔗 Links

- [Next.js Documentation](https://nextjs.org/docs)
- [Wise API Documentation](https://api-docs.wise.com/)
- [ExchangeRate.host Documentation](https://exchangerate.host/)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
