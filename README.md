# FX Compare - Live Exchange Rate Comparison

A comprehensive forex rate comparison website built with Next.js 14, TypeScript, and Tailwind CSS. Compare live exchange rates from multiple providers including Wise and ExchangeRate.host with server-side rendering and intelligent caching.

## 🚀 Features

- **Live Exchange Rates**: Real-time data from multiple providers
- **Server-Side Rendering**: Fast initial page loads with SEO optimization
- **Conservative Caching**: ISR with 2-hour revalidation to minimize API usage
- **Best Rate Highlighting**: Automatically identifies and highlights the best rates
- **Currency Converter**: Real-time conversion calculator
- **Currency Selector**: Easy-to-use interface for selecting currency pairs
- **Responsive Design**: Mobile-first design with Tailwind CSS
- **Client-Side Updates**: Auto-refresh every 2 hours with SWR (conservative API usage)
- **Error Handling**: Graceful fallbacks for API failures
- **TypeScript**: Full type safety throughout the application

## ⚠️ Important Note

**Demo Limitation**: This application currently integrates with Wise (a real exchange platform) and uses data providers for demonstration purposes. For a production forex comparison service, you should integrate with multiple real exchange platforms where users can actually transfer money. See `REAL_EXCHANGE_PLATFORMS.md` for detailed guidance on integrating with real money transfer services.

## 🏗️ Tech Stack

- **Framework**: Next.js 14 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS 4
- **Data Fetching**: SWR for client-side, native fetch for server-side
- **UI Components**: Headless UI + Heroicons
- **Deployment**: Vercel-ready

## 📋 Prerequisites

- Node.js 18+
- npm, yarn, or pnpm
- Wise API token (optional, for Wise rates)

## 🛠️ Installation & Setup

### 1. Clone and Install Dependencies

```bash
git clone <repository-url>
cd fxcompare
npm install
```

### 2. Environment Configuration

Create a `.env.local` file in the root directory:

```bash
cp .env.local.example .env.local
```

Edit `.env.local` with your configuration:

```env
# Wise API Configuration (optional)
# Get your API token from https://api-docs.wise.com/
WISE_API_TOKEN=your_wise_api_token_here

# Application URL
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

**Note**: The application will work without the Wise API token, using only ExchangeRate.host data.

### 3. Start Development Server

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to view the application.

## 🌐 Usage

### Viewing Exchange Rates

Navigate to any currency pair using the format:
```
http://localhost:3000/[BASE]/[QUOTE]
```

Examples:
- `/USD/EUR` - US Dollar to Euro
- `/GBP/USD` - British Pound to US Dollar
- `/EUR/JPY` - Euro to Japanese Yen

### Supported Currencies

The application supports major currencies including:
USD, EUR, GBP, JPY, AUD, CAD, CHF, CNY, SEK, NZD, MXN, SGD, HKD, NOK, TRY, ZAR, BRL, INR, KRW, PLN, THB, IDR, HUF, CZK, ILS, CLP, PHP, AED, COP, SAR, MYR, RON, BGN, HRK, RUB, DKK, ISK

## 🏗️ Project Structure

```
src/
├── app/
│   ├── [base]/[quote]/          # Dynamic currency pair routes
│   │   ├── page.tsx             # Main comparison page
│   │   └── ClientRefresh.tsx    # Client-side refresh component
│   ├── api/rates/
│   │   └── route.ts             # API endpoint for rate fetching
│   ├── globals.css              # Global styles
│   ├── layout.tsx               # Root layout
│   └── page.tsx                 # Homepage
├── components/
│   ├── ConversionCalc.tsx       # Currency conversion calculator
│   └── RateTable.tsx            # Rate comparison table
├── data/
│   └── providers.json           # Provider metadata
├── hooks/
│   └── useRates.ts              # SWR hook for client-side fetching
├── lib/
│   ├── providers/
│   │   ├── exchangerateHost.ts  # ExchangeRate.host integration
│   │   └── wise.ts              # Wise API integration
│   └── aggregate.ts             # Rate aggregation logic
└── types/
    └── index.ts                 # TypeScript type definitions
```

## 🔧 API Integration

### Wise API
- **Endpoint**: `https://api.transferwise.com/v1/rates`
- **Authentication**: Bearer token
- **Cache**: 2 hours (7200 seconds)
- **Rate Type**: Mid-market rates

### ExchangeRate-API.com
- **Endpoint**: `https://api.exchangerate-api.com/v4/latest`
- **Authentication**: None required (free tier: 1,500 requests/month)
- **Cache**: 2 hours (7200 seconds)
- **Rate Type**: Mid-market with synthetic spread

## 📊 API Usage & Limits

### Conservative Caching Strategy
Our caching strategy is designed to minimize API usage while providing useful exchange rate data:

- **2-hour server-side cache**: Reduces API calls by 97% compared to no caching
- **Maximum 12 API calls per currency pair per day** (24 hours ÷ 2 hours)
- **Estimated monthly usage**: 50-120 calls for typical usage patterns

### Free Tier Limits
- **ExchangeRate-API**: 1,500 requests/month (free)
- **Wise API**: Generous limits for personal use
- **Recommendation**: Monitor usage via `API_USAGE_ANALYSIS.md`

### Scaling Considerations
For production use with higher traffic:
1. Upgrade ExchangeRate-API ($10/month for 100,000 requests)
2. Implement Redis caching for longer cache durations
3. Add background jobs to update rates via cron
4. Consider rate limiting per IP address

See `API_USAGE_ANALYSIS.md` for detailed usage calculations and optimization strategies.

## 🚀 Deployment

### Vercel Deployment (Recommended)

1. **Connect Repository**:
   ```bash
   # Push to GitHub/GitLab/Bitbucket
   git push origin main
   ```

2. **Deploy to Vercel**:
   - Visit [vercel.com](https://vercel.com)
   - Import your repository
   - Configure environment variables:
     - `WISE_API_TOKEN` (optional)
     - `NEXT_PUBLIC_APP_URL` (your domain)

3. **Environment Variables in Vercel**:
   - Go to Project Settings → Environment Variables
   - Add your variables for Production, Preview, and Development

### Manual Deployment

```bash
# Build the application
npm run build

# Start production server
npm start
```

### Docker Deployment

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
EXPOSE 3000
CMD ["npm", "start"]
```

## 🧪 Development

### Available Scripts

```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
```

### Adding New Providers

1. Create provider file in `src/lib/providers/`
2. Implement the provider interface
3. Add to `src/lib/aggregate.ts`
4. Update `src/data/providers.json`

Example provider structure:
```typescript
export async function fetchNewProvider(base: string, quote: string): Promise<Rate | null> {
  // Implementation
}
```

## 🔍 Monitoring & Performance

### Caching Strategy (Conservative API Usage)
- **Server-side**: ISR with 2-hour revalidation (reduces API calls by 97%)
- **Client-side**: SWR with 2-hour refresh interval
- **API responses**: 2-hour cache headers with 4-hour stale-while-revalidate
- **Trade-off**: Prioritizes cost efficiency over real-time updates

### Error Handling
- Graceful API failure handling
- Fallback to available providers
- User-friendly error messages
- Automatic retry mechanisms

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/new-feature`
3. Commit changes: `git commit -am 'Add new feature'`
4. Push to branch: `git push origin feature/new-feature`
5. Submit a Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Create an issue on GitHub
- Check the documentation
- Review the API provider documentation

## 🔗 Links

- [Next.js Documentation](https://nextjs.org/docs)
- [Wise API Documentation](https://api-docs.wise.com/)
- [ExchangeRate.host Documentation](https://exchangerate.host/)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
