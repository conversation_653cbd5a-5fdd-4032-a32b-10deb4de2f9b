import { Rate } from '@/types';
import { fetchWise } from './providers/wise';
import { fetchExHost } from './providers/exchangerateHost';

/**
 * Fetches rates from all providers and aggregates them
 * @param base Base currency (e.g., 'USD')
 * @param quote Quote currency (e.g., 'EUR')
 * @returns Promise<Rate[]> Array of rates sorted by highest buy rate
 */
export async function getRates(base: string, quote: string): Promise<Rate[]> {
  const rates: Rate[] = [];
  
  // Validate currency codes
  if (!base || !quote || base.length !== 3 || quote.length !== 3) {
    throw new Error('Invalid currency codes. Must be 3-letter ISO codes.');
  }

  // Convert to uppercase for consistency
  const baseCurrency = base.toUpperCase();
  const quoteCurrency = quote.toUpperCase();

  // Fetch from all providers in parallel
  const providers = [
    fetchWise(baseCurrency, quoteCurrency),
    fetchExHost(baseCurrency, quoteCurrency),
  ];

  try {
    const results = await Promise.allSettled(providers);
    
    results.forEach((result, index) => {
      if (result.status === 'fulfilled' && result.value) {
        rates.push(result.value);
      } else if (result.status === 'rejected') {
        console.error(`Provider ${index} failed:`, result.reason);
      }
    });

    // Sort by highest buy rate (best rate for buying the quote currency)
    rates.sort((a, b) => b.buyRate - a.buyRate);

    return rates;
  } catch (error) {
    console.error('Error aggregating rates:', error);
    throw new Error('Failed to fetch rates from providers');
  }
}

/**
 * Gets the best rate from the aggregated rates
 * @param rates Array of rates
 * @returns Rate | null The best rate or null if no rates available
 */
export function getBestRate(rates: Rate[]): Rate | null {
  if (rates.length === 0) return null;
  
  // Rates are already sorted by highest buy rate
  return rates[0];
}

/**
 * Calculates conversion amount using the best available rate
 * @param amount Amount to convert
 * @param rates Array of rates
 * @returns number Converted amount
 */
export function calculateConversion(amount: number, rates: Rate[]): number {
  const bestRate = getBestRate(rates);
  if (!bestRate) return 0;
  
  return amount * bestRate.buyRate;
}

/**
 * Formats currency amount for display
 * @param amount Numeric amount
 * @param currency Currency code
 * @returns string Formatted currency string
 */
export function formatCurrency(amount: number, currency: string): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 6,
  }).format(amount);
}

/**
 * Formats rate for display
 * @param rate Numeric rate
 * @returns string Formatted rate string
 */
export function formatRate(rate: number): string {
  return rate.toFixed(6);
}
