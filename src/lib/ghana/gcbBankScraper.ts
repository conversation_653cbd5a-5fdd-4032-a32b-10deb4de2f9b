import { GhanaBankRate } from '@/types';

/**
 * Scrapes exchange rates from GCB Bank Ghana website
 * URL: https://www.gcbbank.com.gh/87-exchange/447-foreign-exchange
 * 
 * Note: This is web scraping and should be used responsibly
 * - Respects rate limiting
 * - Includes proper error handling
 * - Caches results to minimize requests
 * - Includes disclaimers about rate accuracy
 */

interface GCBRateData {
  currency: string;
  buying: number;
  selling: number;
}

export async function scrapeGCBBankRates(): Promise<GhanaBankRate[]> {
  try {
    console.log('Scraping GCB Bank exchange rates...');
    
    const url = 'https://www.gcbbank.com.gh/87-exchange/447-foreign-exchange';
    
    const response = await fetch(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; FXCompare/1.0; +https://fxcompare.com)',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Cache-Control': 'no-cache',
      },
      next: { revalidate: 7200 }, // Cache for 2 hours
    });

    if (!response.ok) {
      throw new Error(`GCB Bank website returned ${response.status}: ${response.statusText}`);
    }

    const html = await response.text();
    
    // Parse the HTML to extract exchange rates
    const rates = parseGCBRates(html);
    
    if (rates.length === 0) {
      console.warn('No exchange rates found on GCB Bank website');
      return [];
    }

    // Convert to our standard format
    const ghanaBankRates: GhanaBankRate[] = rates.map(rate => ({
      bank: 'gcb-bank',
      currency: rate.currency,
      buyingRate: rate.buying,
      sellingRate: rate.selling,
      lastUpdated: new Date().toISOString(),
      source: 'web-scraping' as const,
    }));

    console.log(`Successfully scraped ${ghanaBankRates.length} rates from GCB Bank`);
    return ghanaBankRates;

  } catch (error) {
    console.error('Error scraping GCB Bank rates:', error);
    
    // Return empty array instead of throwing to allow other providers to work
    return [];
  }
}

/**
 * Parses HTML content from GCB Bank to extract exchange rates
 * This function looks for the rate table structure
 */
function parseGCBRates(html: string): GCBRateData[] {
  const rates: GCBRateData[] = [];
  
  try {
    // Look for the exchange rate table
    // Based on the HTML structure we saw: "Stay up to dateAs at 3rd Jul. 2025"
    // followed by a table with Currency, Buying, Selling columns
    
    // Simple regex-based parsing (in production, consider using a proper HTML parser)
    const tableRegex = /Currency\s+Buying\s+Selling([\s\S]*?)(?=Stay up to date|$)/i;
    const tableMatch = html.match(tableRegex);
    
    if (!tableMatch) {
      console.warn('Could not find exchange rate table in GCB Bank HTML');
      return rates;
    }
    
    const tableContent = tableMatch[1];
    
    // Parse individual rate rows
    // Looking for patterns like: USD 10.3000 10.6500
    const rateRegex = /([A-Z]{3})\s+([\d.]+)\s+([\d.]+)/g;
    let match;
    
    while ((match = rateRegex.exec(tableContent)) !== null) {
      const [, currency, buying, selling] = match;
      
      const buyingRate = parseFloat(buying);
      const sellingRate = parseFloat(selling);
      
      if (!isNaN(buyingRate) && !isNaN(sellingRate)) {
        rates.push({
          currency: currency.toUpperCase(),
          buying: buyingRate,
          selling: sellingRate,
        });
      }
    }
    
    // If regex parsing fails, try alternative parsing methods
    if (rates.length === 0) {
      console.warn('Regex parsing failed, attempting alternative parsing...');
      return parseGCBRatesAlternative(html);
    }
    
    return rates;
    
  } catch (error) {
    console.error('Error parsing GCB Bank HTML:', error);
    return rates;
  }
}

/**
 * Alternative parsing method for GCB Bank rates
 * Uses different patterns to extract rate data
 */
function parseGCBRatesAlternative(html: string): GCBRateData[] {
  const rates: GCBRateData[] = [];
  
  try {
    // Look for specific currency patterns in the HTML
    const currencyPatterns = [
      { currency: 'USD', pattern: /USD[\s\S]*?([\d.]+)[\s\S]*?([\d.]+)/i },
      { currency: 'GBP', pattern: /GBP[\s\S]*?([\d.]+)[\s\S]*?([\d.]+)/i },
      { currency: 'EUR', pattern: /EUR[\s\S]*?([\d.]+)[\s\S]*?([\d.]+)/i },
      { currency: 'JPY', pattern: /YEN[\s\S]*?([\d.]+)[\s\S]*?([\d.]+)/i },
      { currency: 'CAD', pattern: /CAD[\s\S]*?([\d.]+)[\s\S]*?([\d.]+)/i },
    ];
    
    for (const { currency, pattern } of currencyPatterns) {
      const match = html.match(pattern);
      if (match) {
        const buying = parseFloat(match[1]);
        const selling = parseFloat(match[2]);
        
        if (!isNaN(buying) && !isNaN(selling)) {
          rates.push({
            currency,
            buying,
            selling,
          });
        }
      }
    }
    
    return rates;
    
  } catch (error) {
    console.error('Error in alternative GCB Bank parsing:', error);
    return rates;
  }
}

/**
 * Mock function for development/testing when scraping is not available
 * Returns realistic sample data for GCB Bank
 */
export function getMockGCBRates(): GhanaBankRate[] {
  return [
    {
      bank: 'gcb-bank',
      currency: 'USD',
      buyingRate: 10.3000,
      sellingRate: 10.6500,
      lastUpdated: new Date().toISOString(),
      source: 'web-scraping',
    },
    {
      bank: 'gcb-bank',
      currency: 'GBP',
      buyingRate: 14.1000,
      sellingRate: 14.6200,
      lastUpdated: new Date().toISOString(),
      source: 'web-scraping',
    },
    {
      bank: 'gcb-bank',
      currency: 'EUR',
      buyingRate: 12.0500,
      sellingRate: 12.5800,
      lastUpdated: new Date().toISOString(),
      source: 'web-scraping',
    },
  ];
}
