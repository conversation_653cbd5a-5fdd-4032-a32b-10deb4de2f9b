import { GhanaBankRate } from '@/types';
import { scrapeGCBBankRates, getMockGCBRates } from './gcbBankScraper';

/**
 * Aggregates exchange rates from Ghana banks
 * Currently supports:
 * - GCB Bank (web scraping)
 * 
 * Future banks to add:
 * - Standard Chartered Ghana
 * - Ecobank Ghana
 * - Absa Bank Ghana
 * - Fidelity Bank Ghana
 * - Bank of Ghana (official rates)
 */

export async function getGhanaBankRates(baseCurrency: string, quoteCurrency: string): Promise<GhanaBankRate[]> {
  const rates: GhanaBankRate[] = [];
  
  // Validate that we're dealing with GHS as either base or quote
  const isGhanaianPair = baseCurrency === 'GHS' || quoteCurrency === 'GHS';
  
  if (!isGhanaianPair) {
    console.log('Not a Ghanaian currency pair, skipping Ghana bank rates');
    return rates;
  }
  
  // Determine which currency we need rates for
  const foreignCurrency = baseCurrency === 'GHS' ? quoteCurrency : baseCurrency;
  
  console.log(`Fetching Ghana bank rates for ${foreignCurrency}/GHS`);
  
  try {
    // Fetch from all available Ghana banks
    const bankPromises = [
      fetchGCBBankRates(foreignCurrency),
      // Add more banks here as they become available
      // fetchStandardCharteredGhanaRates(foreignCurrency),
      // fetchEcobankGhanaRates(foreignCurrency),
    ];
    
    const results = await Promise.allSettled(bankPromises);
    
    results.forEach((result, index) => {
      if (result.status === 'fulfilled' && result.value.length > 0) {
        rates.push(...result.value);
      } else if (result.status === 'rejected') {
        console.error(`Ghana bank ${index} failed:`, result.reason);
      }
    });
    
    // Sort by best buying rate (highest rate when selling foreign currency for GHS)
    rates.sort((a, b) => b.buyingRate - a.buyingRate);
    
    console.log(`Successfully aggregated ${rates.length} rates from Ghana banks`);
    return rates;
    
  } catch (error) {
    console.error('Error aggregating Ghana bank rates:', error);
    return rates;
  }
}

/**
 * Fetches rates from GCB Bank for a specific foreign currency
 */
async function fetchGCBBankRates(foreignCurrency: string): Promise<GhanaBankRate[]> {
  try {
    // Check if we're in development mode or if scraping should be disabled
    const isDevelopment = process.env.NODE_ENV === 'development';
    const disableScraping = process.env.DISABLE_BANK_SCRAPING === 'true';
    
    if (isDevelopment || disableScraping) {
      console.log('Using mock GCB Bank rates (development mode or scraping disabled)');
      return getMockGCBRates().filter(rate => rate.currency === foreignCurrency);
    }
    
    // Attempt to scrape live rates
    const allRates = await scrapeGCBBankRates();
    
    // Filter for the specific currency we need
    const filteredRates = allRates.filter(rate => rate.currency === foreignCurrency);
    
    if (filteredRates.length === 0) {
      console.warn(`No ${foreignCurrency} rates found from GCB Bank, using mock data`);
      return getMockGCBRates().filter(rate => rate.currency === foreignCurrency);
    }
    
    return filteredRates;
    
  } catch (error) {
    console.error('Error fetching GCB Bank rates:', error);
    
    // Fallback to mock data
    console.log('Falling back to mock GCB Bank rates');
    return getMockGCBRates().filter(rate => rate.currency === foreignCurrency);
  }
}

/**
 * Converts Ghana bank rates to the standard Rate format for comparison
 * This allows Ghana bank rates to be displayed alongside international exchange platforms
 */
export function convertGhanaBankRatesToStandardFormat(
  ghanaBankRates: GhanaBankRate[],
  baseCurrency: string,
  quoteCurrency: string
): import('@/types').Rate[] {
  return ghanaBankRates.map(bankRate => {
    // Determine if we need to invert the rate
    const isInverted = baseCurrency === 'GHS';
    
    let buyRate: number;
    let sellRate: number;
    
    if (isInverted) {
      // Converting from GHS to foreign currency
      // Bank's selling rate becomes our buy rate (we buy foreign currency)
      buyRate = 1 / bankRate.sellingRate;
      sellRate = 1 / bankRate.buyingRate;
    } else {
      // Converting from foreign currency to GHS
      // Bank's buying rate is what we get when selling foreign currency
      buyRate = bankRate.buyingRate;
      sellRate = bankRate.sellingRate;
    }
    
    const spread = sellRate - buyRate;
    
    return {
      provider: bankRate.bank,
      buyRate,
      sellRate,
      timestamp: bankRate.lastUpdated,
      spread,
    };
  });
}

/**
 * Gets the best Ghana bank rate for a currency pair
 */
export function getBestGhanaBankRate(rates: GhanaBankRate[]): GhanaBankRate | null {
  if (rates.length === 0) return null;
  
  // Rates are already sorted by best buying rate
  return rates[0];
}

/**
 * Formats Ghana bank rate for display
 */
export function formatGhanaBankRate(rate: GhanaBankRate): string {
  return `${rate.currency}: Buy ${rate.buyingRate.toFixed(4)}, Sell ${rate.sellingRate.toFixed(4)}`;
}

/**
 * Checks if a currency pair involves Ghana Cedi
 */
export function isGhanaianCurrencyPair(base: string, quote: string): boolean {
  return base === 'GHS' || quote === 'GHS';
}

/**
 * Gets supported currencies for Ghana banking
 */
export function getSupportedGhanaCurrencies(): string[] {
  return ['USD', 'GBP', 'EUR', 'JPY', 'CAD', 'CFA'];
}
