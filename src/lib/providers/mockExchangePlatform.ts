import { Rate } from '@/types';

/**
 * Mock exchange platform provider for demonstration purposes
 * In production, this should be replaced with real exchange platform APIs
 * such as Remitly, XE Money Transfer, CurrencyFair, etc.
 * 
 * This mock simulates what rates from a real exchange platform might look like:
 * - Slightly worse rates than mid-market (realistic spreads)
 * - Transfer fees built into the rate
 * - More realistic business model
 */
export async function fetchMockExchangePlatform(base: string, quote: string): Promise<Rate | null> {
  try {
    // For demo purposes, we'll fetch from a free API and adjust the rates
    // to simulate what a real exchange platform might offer
    const url = `https://api.exchangerate-api.com/v4/latest/${base}`;
    
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
      },
      next: { revalidate: 7200 }, // Cache for 2 hours
    });

    if (!response.ok) {
      throw new Error(`Mock Exchange Platform API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    
    if (!data.rates || !data.rates[quote]) {
      throw new Error(`No rate found for ${base}/${quote} from Mock Exchange Platform`);
    }

    const midMarketRate = data.rates[quote];
    
    // Simulate realistic exchange platform rates:
    // - Add a 2-3% spread (typical for money transfer services)
    // - This represents the real cost of using an exchange platform
    const platformSpread = midMarketRate * 0.025; // 2.5% spread
    const buyRate = midMarketRate - platformSpread; // Rate you get when buying
    const sellRate = midMarketRate + platformSpread; // Rate you pay when selling
    
    const rate: Rate = {
      provider: 'mock-exchange-platform',
      buyRate: buyRate,
      sellRate: sellRate,
      timestamp: new Date().toISOString(),
      spread: platformSpread,
    };

    return rate;
  } catch (error) {
    console.error('Error fetching Mock Exchange Platform rates:', error);
    return null;
  }
}

/**
 * Alternative mock for a different exchange platform
 * This simulates a more competitive platform with better rates
 */
export async function fetchMockCompetitorPlatform(base: string, quote: string): Promise<Rate | null> {
  try {
    const url = `https://api.exchangerate-api.com/v4/latest/${base}`;
    
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
      },
      next: { revalidate: 7200 },
    });

    if (!response.ok) {
      throw new Error(`Mock Competitor Platform API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    
    if (!data.rates || !data.rates[quote]) {
      throw new Error(`No rate found for ${base}/${quote} from Mock Competitor Platform`);
    }

    const midMarketRate = data.rates[quote];
    
    // Simulate a more competitive platform with 1.5% spread
    const platformSpread = midMarketRate * 0.015; // 1.5% spread
    const buyRate = midMarketRate - platformSpread;
    const sellRate = midMarketRate + platformSpread;
    
    const rate: Rate = {
      provider: 'mock-competitor-platform',
      buyRate: buyRate,
      sellRate: sellRate,
      timestamp: new Date().toISOString(),
      spread: platformSpread,
    };

    return rate;
  } catch (error) {
    console.error('Error fetching Mock Competitor Platform rates:', error);
    return null;
  }
}
