import { Rate, WiseApiResponse } from '@/types';

export async function fetchWise(base: string, quote: string): Promise<Rate | null> {
  const apiToken = process.env.WISE_API_TOKEN;
  
  if (!apiToken) {
    console.warn('WISE_API_TOKEN not configured');
    return null;
  }

  try {
    const url = `https://api.transferwise.com/v1/rates?source=${base}&target=${quote}`;
    
    const response = await fetch(url, {
      headers: {
        'Authorization': `Bearer ${apiToken}`,
        'Content-Type': 'application/json',
      },
      next: { revalidate: 60 }, // Cache for 60 seconds
    });

    if (!response.ok) {
      throw new Error(`Wise API error: ${response.status} ${response.statusText}`);
    }

    const data: WiseApiResponse[] = await response.json();
    
    if (!data || data.length === 0) {
      throw new Error('No rate data received from Wise');
    }

    const rateData = data[0];
    
    // Wise typically provides mid-market rates, so we'll use the same rate for buy/sell
    // In a real implementation, you might want to add a small spread
    const rate: Rate = {
      provider: 'wise',
      buyRate: rateData.rate,
      sellRate: rateData.rate,
      timestamp: rateData.time || new Date().toISOString(),
      spread: 0,
    };

    return rate;
  } catch (error) {
    console.error('Error fetching Wise rates:', error);
    return null;
  }
}
