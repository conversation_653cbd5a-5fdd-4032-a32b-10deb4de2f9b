import { Rate, ExchangeRateHostResponse } from '@/types';

export async function fetchExHost(base: string, quote: string): Promise<Rate | null> {
  try {
    const url = `https://api.exchangerate.host/latest?base=${base}&symbols=${quote}`;
    
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
      },
      next: { revalidate: 300 }, // Cache for 5 minutes
    });

    if (!response.ok) {
      throw new Error(`ExchangeRate.host API error: ${response.status} ${response.statusText}`);
    }

    const data: ExchangeRateHostResponse = await response.json();

    console.log('ExchangeRate.host response:', data);

    if (!data.success) {
      throw new Error(`ExchangeRate.host API returned unsuccessful response: ${JSON.stringify(data)}`);
    }

    const rateValue = data.rates[quote];
    
    if (typeof rateValue !== 'number') {
      throw new Error(`No rate found for ${base}/${quote} from ExchangeRate.host`);
    }

    // ExchangeRate.host provides mid-market rates
    // Adding a small spread for demonstration (0.1%)
    const spread = rateValue * 0.001;
    
    const rate: Rate = {
      provider: 'exchangerate-host',
      buyRate: rateValue - spread / 2,
      sellRate: rateValue + spread / 2,
      timestamp: new Date().toISOString(),
      spread: spread,
    };

    return rate;
  } catch (error) {
    console.error('Error fetching ExchangeRate.host rates:', error);
    return null;
  }
}
