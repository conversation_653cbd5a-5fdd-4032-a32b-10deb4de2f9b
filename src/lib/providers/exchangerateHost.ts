import { Rate, ExchangeRateHostResponse } from '@/types';

export async function fetchExHost(base: string, quote: string): Promise<Rate | null> {
  try {
    // Try ExchangeRate.host first (if API key is available)
    const apiKey = process.env.EXCHANGERATE_HOST_API_KEY;

    if (apiKey) {
      return await fetchFromExchangeRateHost(base, quote, apiKey);
    }

    // Fallback to exchangerate-api.io (free alternative)
    return await fetchFromExchangeRateApi(base, quote);
  } catch (error) {
    console.error('Error fetching exchange rates:', error);
    return null;
  }
}

async function fetchFromExchangeRateHost(base: string, quote: string, apiKey: string): Promise<Rate | null> {
  const url = `https://api.exchangerate.host/latest?access_key=${apiKey}&base=${base}&symbols=${quote}`;

  const response = await fetch(url, {
    headers: {
      'Content-Type': 'application/json',
    },
    next: { revalidate: 300 }, // Cache for 5 minutes
  });

  if (!response.ok) {
    throw new Error(`ExchangeRate.host API error: ${response.status} ${response.statusText}`);
  }

  const data: ExchangeRateHostResponse = await response.json();

  if (!data.success) {
    throw new Error(`ExchangeRate.host API returned unsuccessful response`);
  }

  const rateValue = data.rates[quote];

  if (typeof rateValue !== 'number') {
    throw new Error(`No rate found for ${base}/${quote} from ExchangeRate.host`);
  }

  const spread = rateValue * 0.001;

  return {
    provider: 'exchangerate-host',
    buyRate: rateValue - spread / 2,
    sellRate: rateValue + spread / 2,
    timestamp: new Date().toISOString(),
    spread: spread,
  };
}

async function fetchFromExchangeRateApi(base: string, quote: string): Promise<Rate | null> {
  // Using exchangerate-api.io as a free alternative
  const url = `https://api.exchangerate-api.com/v4/latest/${base}`;

  const response = await fetch(url, {
    headers: {
      'Content-Type': 'application/json',
    },
    next: { revalidate: 300 }, // Cache for 5 minutes
  });

  if (!response.ok) {
    throw new Error(`ExchangeRate API error: ${response.status} ${response.statusText}`);
  }

  const data = await response.json();

  if (!data.rates || !data.rates[quote]) {
    throw new Error(`No rate found for ${base}/${quote} from ExchangeRate API`);
  }

  const rateValue = data.rates[quote];
  const spread = rateValue * 0.001;

  return {
    provider: 'exchangerate-api',
    buyRate: rateValue - spread / 2,
    sellRate: rateValue + spread / 2,
    timestamp: new Date().toISOString(),
    spread: spread,
  };
}
