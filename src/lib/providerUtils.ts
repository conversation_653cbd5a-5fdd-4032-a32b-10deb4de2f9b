import { Rate } from '@/types';
import providersData from '@/data/providers.json';

/**
 * Generates dynamic provider attribution text based on actual providers in the rates data
 * @param rates Array of rates from different providers
 * @returns String with provider names for attribution
 */
export function getProviderAttribution(rates: Rate[]): string {
  if (!rates || rates.length === 0) {
    return 'No data providers available';
  }

  // Get unique provider names from the rates
  const uniqueProviders = [...new Set(rates.map(rate => rate.provider))];
  
  // Map provider IDs to display names
  const providerNames = uniqueProviders
    .map(providerId => {
      const provider = providersData[providerId as keyof typeof providersData];
      return provider?.name || providerId;
    })
    .sort(); // Sort alphabetically for consistency

  // Format the attribution text
  if (providerNames.length === 1) {
    return `Data provided by ${providerNames[0]}`;
  } else if (providerNames.length === 2) {
    return `Data provided by ${providerNames[0]} and ${providerNames[1]}`;
  } else {
    const lastProvider = providerNames.pop();
    return `Data provided by ${providerNames.join(', ')}, and ${lastProvider}`;
  }
}

/**
 * Generates a short attribution for table footers
 * @param rates Array of rates from different providers
 * @returns Short attribution string
 */
export function getShortProviderAttribution(rates: Rate[]): string {
  if (!rates || rates.length === 0) {
    return 'No data available';
  }

  const uniqueProviders = [...new Set(rates.map(rate => rate.provider))];
  const providerNames = uniqueProviders
    .map(providerId => {
      const provider = providersData[providerId as keyof typeof providersData];
      return provider?.name || providerId;
    })
    .sort();

  if (providerNames.length === 1) {
    return `Data from ${providerNames[0]}`;
  } else if (providerNames.length === 2) {
    return `Data from ${providerNames[0]} and ${providerNames[1]}`;
  } else {
    const lastProvider = providerNames.pop();
    return `Data from ${providerNames.join(', ')}, and ${lastProvider}`;
  }
}
