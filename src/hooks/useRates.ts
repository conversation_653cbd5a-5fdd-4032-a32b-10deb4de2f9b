'use client';

import useS<PERSON> from 'swr';
import { RatesApiResponse, UseRatesReturn, ApiError } from '@/types';

// Fetcher function for SWR
const fetcher = async (url: string): Promise<RatesApiResponse> => {
  const response = await fetch(url);
  
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
  }
  
  return response.json();
};

/**
 * Custom hook for fetching exchange rates using SWR
 * @param base Base currency code (e.g., 'USD')
 * @param quote Quote currency code (e.g., 'EUR')
 * @returns UseRatesReturn object with data, error, loading state, and mutate function
 */
export function useRates(base: string, quote: string): UseRatesReturn {
  // Construct the API URL
  const url = base && quote 
    ? `/api/rates?base=${encodeURIComponent(base)}&quote=${encodeURIComponent(quote)}`
    : null;

  const {
    data,
    error,
    isLoading,
    mutate,
  } = useSWR<RatesApiResponse, Error>(
    url,
    fetcher,
    {
      refreshInterval: 7200000, // Refresh every 2 hours (7200000ms)
      revalidateOnFocus: false, // Don't refresh on window focus to save API calls
      revalidateOnReconnect: true, // Still refresh on reconnect for reliability
      dedupingInterval: 300000, // Dedupe requests within 5 minutes
      errorRetryCount: 2, // Reduce retry attempts to save API calls
      errorRetryInterval: 10000, // Increase retry interval
      onError: (error) => {
        console.error('Error fetching rates:', error);
      },
    }
  );

  // Transform error to our ApiError type
  const apiError: ApiError | undefined = error ? {
    message: error.message,
    code: 'FETCH_ERROR',
  } : undefined;

  return {
    data: data?.data,
    error: apiError,
    isLoading,
    mutate,
  };
}

/**
 * Hook for fetching rates without automatic refresh (for one-time fetches)
 * @param base Base currency code
 * @param quote Quote currency code
 * @returns UseRatesReturn object
 */
export function useRatesOnce(base: string, quote: string): UseRatesReturn {
  const url = base && quote 
    ? `/api/rates?base=${encodeURIComponent(base)}&quote=${encodeURIComponent(quote)}`
    : null;

  const {
    data,
    error,
    isLoading,
    mutate,
  } = useSWR<RatesApiResponse, Error>(
    url,
    fetcher,
    {
      revalidateOnFocus: false,
      revalidateOnReconnect: false,
      refreshInterval: 0, // No auto-refresh for one-time fetches
      dedupingInterval: 3600000, // Dedupe for 1 hour
    }
  );

  const apiError: ApiError | undefined = error ? {
    message: error.message,
    code: 'FETCH_ERROR',
  } : undefined;

  return {
    data: data?.data,
    error: apiError,
    isLoading,
    mutate,
  };
}
