{"gcb-bank": {"id": "gcb-bank", "name": "GCB Bank PLC", "website": "https://www.gcbbank.com.gh", "logoUrl": "https://www.gcbbank.com.gh/templates/gcb2017/images/logo.png", "hasAPI": false, "scrapingEnabled": true, "exchangeRateUrl": "https://www.gcbbank.com.gh/87-exchange/447-foreign-exchange", "supportedCurrencies": ["USD", "GBP", "EUR", "JPY", "CAD", "CFA"]}, "standard-chartered-ghana": {"id": "standard-chartered-ghana", "name": "Standard Chartered Ghana", "website": "https://www.sc.com/gh", "logoUrl": "https://av.sc.com/gh/content/images/sc-logo.png", "hasAPI": false, "scrapingEnabled": false, "exchangeRateUrl": "https://www.sc.com/gh/invest/foreign-exchange/", "supportedCurrencies": ["USD", "EUR", "GBP", "AUD", "CAD", "CHF", "JPY", "AED", "ZAR"], "note": "Rates not publicly displayed, requires investigation"}, "ecobank-ghana": {"id": "ecobank-ghana", "name": "Ecobank Ghana", "website": "https://ecobank.com/gh", "logoUrl": "https://ecobank.com/assets/images/ecobank-logo.png", "hasAPI": false, "scrapingEnabled": false, "exchangeRateUrl": null, "supportedCurrencies": [], "note": "Exchange rates section not found on public website"}, "absa-bank-ghana": {"id": "absa-bank-ghana", "name": "Absa Bank Ghana", "website": "https://www.absa.com.gh", "logoUrl": "https://www.absa.com.gh/content/dam/africa/absa/gh/images/logos/absa-logo.png", "hasAPI": false, "scrapingEnabled": false, "exchangeRateUrl": null, "supportedCurrencies": [], "note": "Requires investigation for exchange rate availability"}, "fidelity-bank-ghana": {"id": "fidelity-bank-ghana", "name": "Fidelity Bank Ghana", "website": "https://www.fidelitybank.com.gh", "logoUrl": "https://www.fidelitybank.com.gh/assets/images/fidelity-logo.png", "hasAPI": false, "scrapingEnabled": false, "exchangeRateUrl": null, "supportedCurrencies": [], "note": "Requires investigation for exchange rate availability"}, "calbank": {"id": "calbank", "name": "CAL Bank", "website": "https://calbank.net", "logoUrl": "https://calbank.net/assets/images/cal-bank-logo.png", "hasAPI": false, "scrapingEnabled": false, "exchangeRateUrl": null, "supportedCurrencies": [], "note": "Mentioned in search results, requires investigation"}, "bank-of-ghana": {"id": "bank-of-ghana", "name": "Bank of Ghana", "website": "https://www.bog.gov.gh", "logoUrl": "https://www.bog.gov.gh/wp-content/themes/bog/assets/images/bog-logo.png", "hasAPI": false, "scrapingEnabled": false, "exchangeRateUrl": null, "supportedCurrencies": [], "note": "Central bank - may have official exchange rates"}}