// Core rate data structure
export interface Rate {
  provider: string;
  buyRate: number;
  sellRate: number;
  timestamp: string;
  spread?: number; // Calculated as sellRate - buyRate
}

// Provider metadata
export interface Provider {
  id: string;
  name: string;
  logoUrl: string;
  website?: string;
}

// API response structures
export interface RatesApiResponse {
  base: string;
  quote: string;
  data: Rate[];
  timestamp: string;
}

// Wise API response structure
export interface WiseApiResponse {
  rate: number;
  source: string;
  target: string;
  time: string;
}

// ExchangeRate.host API response structure
export interface ExchangeRateHostResponse {
  motd: {
    msg: string;
    url: string;
  };
  success: boolean;
  base: string;
  date: string;
  rates: {
    [key: string]: number;
  };
}

// Error handling
export interface ApiError {
  message: string;
  provider?: string;
  code?: string;
}

// Hook return types
export interface UseRatesReturn {
  data: Rate[] | undefined;
  error: ApiError | undefined;
  isLoading: boolean;
  mutate: () => void;
}

// Currency pair
export interface CurrencyPair {
  base: string;
  quote: string;
}

// Conversion calculation
export interface ConversionResult {
  amount: number;
  fromCurrency: string;
  toCurrency: string;
  rate: number;
  result: number;
  provider: string;
}
