import { Metadata } from 'next';
import { getGhanaBankRates, getSupportedGhanaCurrencies } from '@/lib/ghana/ghanaBankingAggregate';
import { GhanaBankRate } from '@/types';
import GhanaBankRateTable from '@/components/GhanaBankRateTable';
import GhanaCurrencyConverter from '@/components/GhanaCurrencyConverter';
import CurrencySelector from '@/components/CurrencySelector';
import { BuildingLibraryIcon, InformationCircleIcon } from '@heroicons/react/24/outline';

export const metadata: Metadata = {
  title: 'Ghana Bank Exchange Rates - FX Compare',
  description: 'Compare live exchange rates from Ghanaian banks including GCB Bank, Standard Chartered Ghana, and more. Get the best rates for Ghana Cedi (GHS) transactions.',
  keywords: 'Ghana, GHS, Ghana Cedi, bank rates, exchange rates, GCB Bank, Standard Chartered Ghana, Ecobank Ghana',
};

export default async function GhanaBanksPage() {
  const supportedCurrencies = getSupportedGhanaCurrencies();
  
  // Fetch rates for popular currency pairs
  const popularPairs = [
    { base: 'USD', quote: 'GHS' },
    { base: 'GBP', quote: 'GHS' },
    { base: 'EUR', quote: 'GHS' },
  ];

  const ratesData = await Promise.allSettled(
    popularPairs.map(async ({ base, quote }) => ({
      pair: `${base}/${quote}`,
      base,
      quote,
      rates: await getGhanaBankRates(base, quote),
    }))
  );

  const successfulRates = ratesData
    .filter((result): result is PromiseFulfilledResult<any> => result.status === 'fulfilled')
    .map(result => result.value);

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Header */}
      <header className="bg-gradient-to-r from-red-600 to-yellow-500 text-white shadow-lg">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center">
            <span className="text-4xl mr-4">🇬🇭</span>
            <div>
              <h1 className="text-3xl font-bold">
                Ghana Bank Exchange Rates
              </h1>
              <p className="text-xl opacity-90 mt-2">
                Live rates from Ghanaian banks for Ghana Cedi (GHS) transactions
              </p>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Currency Selector */}
        <CurrencySelector initialBase="USD" initialQuote="GHS" />

        {/* Information Section */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
          <div className="flex items-start">
            <InformationCircleIcon className="h-6 w-6 text-blue-600 mr-3 mt-1 flex-shrink-0" />
            <div>
              <h3 className="text-lg font-semibold text-blue-900 mb-2">
                About Ghana Bank Rates
              </h3>
              <div className="text-blue-800 space-y-2">
                <p>
                  This section provides live exchange rates from major Ghanaian banks for transactions 
                  involving the Ghana Cedi (GHS). These rates are typically used for:
                </p>
                <ul className="list-disc list-inside ml-4 space-y-1">
                  <li>Foreign currency cash transactions at bank branches</li>
                  <li>International wire transfers and remittances</li>
                  <li>Foreign currency account transactions</li>
                  <li>Telegraphic transfers and money transfers</li>
                </ul>
                <p className="font-medium">
                  <strong>Important:</strong> These are indicative rates only. Contact banks directly 
                  for official rates and to complete transactions.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Popular Currency Pairs */}
        <div className="space-y-8">
          {successfulRates.map(({ pair, base, quote, rates }) => (
            <div key={pair} className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Bank Rate Table */}
              <div className="lg:col-span-2">
                <GhanaBankRateTable 
                  rates={rates} 
                  baseCurrency={base} 
                  quoteCurrency={quote} 
                />
              </div>
              
              {/* Currency Converter */}
              <div className="lg:col-span-1">
                <GhanaCurrencyConverter 
                  rates={rates} 
                  baseCurrency={base} 
                  quoteCurrency={quote} 
                />
              </div>
            </div>
          ))}
        </div>

        {/* Supported Banks Section */}
        <div className="bg-white rounded-lg shadow-md p-6 mt-8">
          <h3 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
            <BuildingLibraryIcon className="h-6 w-6 text-red-600 mr-2" />
            Supported Banks
          </h3>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="border border-gray-200 rounded-lg p-4">
              <div className="flex items-center mb-2">
                <div className="h-8 w-8 rounded bg-red-100 flex items-center justify-center mr-3">
                  <BuildingLibraryIcon className="h-5 w-5 text-red-600" />
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900">GCB Bank PLC</h4>
                  <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">Active</span>
                </div>
              </div>
              <p className="text-sm text-gray-600">
                Live rates via web scraping. Supports USD, GBP, EUR, JPY, CAD, CFA.
              </p>
            </div>
            
            <div className="border border-gray-200 rounded-lg p-4 opacity-60">
              <div className="flex items-center mb-2">
                <div className="h-8 w-8 rounded bg-gray-100 flex items-center justify-center mr-3">
                  <BuildingLibraryIcon className="h-5 w-5 text-gray-400" />
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900">Standard Chartered Ghana</h4>
                  <span className="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded">Coming Soon</span>
                </div>
              </div>
              <p className="text-sm text-gray-600">
                Integration in progress. Will support 12+ currencies.
              </p>
            </div>
            
            <div className="border border-gray-200 rounded-lg p-4 opacity-60">
              <div className="flex items-center mb-2">
                <div className="h-8 w-8 rounded bg-gray-100 flex items-center justify-center mr-3">
                  <BuildingLibraryIcon className="h-5 w-5 text-gray-400" />
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900">More Banks</h4>
                  <span className="text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded">Planned</span>
                </div>
              </div>
              <p className="text-sm text-gray-600">
                Ecobank Ghana, Absa Bank Ghana, Fidelity Bank Ghana, and others.
              </p>
            </div>
          </div>
        </div>

        {/* Supported Currencies */}
        <div className="bg-white rounded-lg shadow-md p-6 mt-8">
          <h3 className="text-xl font-semibold text-gray-900 mb-4">
            Supported Currencies
          </h3>
          <div className="flex flex-wrap gap-2">
            {supportedCurrencies.map((currency) => (
              <span 
                key={currency}
                className="px-3 py-1 bg-red-100 text-red-800 rounded-full text-sm font-medium"
              >
                {currency}
              </span>
            ))}
          </div>
          <p className="text-sm text-gray-600 mt-3">
            More currencies will be added as bank integrations expand.
          </p>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-white border-t border-gray-200 mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <p className="text-sm text-gray-500">
              Ghana bank rates are for informational purposes only. Contact banks directly for official rates and transactions.
            </p>
            <p className="text-xs text-gray-400 mt-2">
              © 2024 FX Compare. Built with Next.js 14. Data sourced responsibly from public bank websites.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
