import { NextRequest, NextResponse } from 'next/server';
import { getGhanaBankRates, isGhanaianCurrencyPair } from '@/lib/ghana/ghanaBankingAggregate';
import { GhanaBankingApiResponse } from '@/types';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const base = searchParams.get('base');
    const quote = searchParams.get('quote');

    // Validate required parameters
    if (!base || !quote) {
      return NextResponse.json(
        { error: 'Missing required parameters: base and quote' },
        { status: 400 }
      );
    }

    // Validate currency code format
    if (base.length !== 3 || quote.length !== 3) {
      return NextResponse.json(
        { error: 'Currency codes must be 3 characters long' },
        { status: 400 }
      );
    }

    // Check if this is a Ghanaian currency pair
    if (!isGhanaianCurrencyPair(base, quote)) {
      return NextResponse.json(
        { 
          error: 'This endpoint only supports currency pairs involving Ghana Cedi (GHS)',
          supportedPairs: ['GHS/USD', 'USD/GHS', 'GHS/EUR', 'EUR/GHS', 'GHS/GBP', 'GBP/GHS']
        },
        { status: 400 }
      );
    }

    // Fetch rates from Ghana banks
    const bankRates = await getGhanaBankRates(base, quote);

    // Prepare response
    const response: GhanaBankingApiResponse = {
      banks: bankRates,
      timestamp: new Date().toISOString(),
      baseCurrency: base.toUpperCase(),
      quoteCurrency: quote.toUpperCase(),
    };

    // Set cache headers for conservative caching (2 hours like other endpoints)
    const headers = new Headers();
    headers.set('Cache-Control', 'public, s-maxage=7200, stale-while-revalidate=14400');
    headers.set('Content-Type', 'application/json');

    return NextResponse.json(response, { headers });

  } catch (error) {
    console.error('Ghana banking API route error:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to fetch Ghana bank exchange rates',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Handle OPTIONS for CORS if needed
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
