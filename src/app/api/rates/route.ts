import { NextRequest, NextResponse } from 'next/server';
import { getRates } from '@/lib/aggregate';
import { RatesApiResponse } from '@/types';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const base = searchParams.get('base');
    const quote = searchParams.get('quote');

    // Validate required parameters
    if (!base || !quote) {
      return NextResponse.json(
        { error: 'Missing required parameters: base and quote' },
        { status: 400 }
      );
    }

    // Validate currency code format
    if (base.length !== 3 || quote.length !== 3) {
      return NextResponse.json(
        { error: 'Currency codes must be 3 characters long' },
        { status: 400 }
      );
    }

    // Fetch rates from all providers
    const rates = await getRates(base, quote);

    // Prepare response
    const response: RatesApiResponse = {
      base: base.toUpperCase(),
      quote: quote.toUpperCase(),
      data: rates,
      timestamp: new Date().toISOString(),
    };

    // Set cache headers for client-side caching
    const headers = new Headers();
    headers.set('Cache-Control', 'public, s-maxage=60, stale-while-revalidate=300');
    headers.set('Content-Type', 'application/json');

    return NextResponse.json(response, { headers });
  } catch (error) {
    console.error('API route error:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to fetch exchange rates',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Handle OPTIONS for CORS if needed
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
