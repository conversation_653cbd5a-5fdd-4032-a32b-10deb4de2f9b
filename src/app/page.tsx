import Link from 'next/link';
import { CurrencyDollarIcon, ArrowTrendingUpIcon, GlobeAltIcon } from '@heroicons/react/24/outline';
import CurrencySelector from '@/components/CurrencySelector';

export default function Home() {
  const popularPairs = [
    { base: 'USD', quote: 'EUR', name: 'US Dollar to Euro' },
    { base: 'USD', quote: 'GBP', name: 'US Dollar to British Pound' },
    { base: 'EUR', quote: 'GBP', name: 'Euro to British Pound' },
    { base: 'USD', quote: 'JPY', name: 'US Dollar to Japanese Yen' },
    { base: 'GBP', quote: 'USD', name: 'British Pound to US Dollar' },
    { base: 'EUR', quote: 'USD', name: 'Euro to US Dollar' },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center">
            <CurrencyDollarIcon className="h-8 w-8 text-blue-600 mr-3" />
            <div>
              <h1 className="text-3xl font-bold text-gray-900">FX Compare</h1>
              <p className="text-gray-600">Live exchange rate comparison</p>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            Compare Live Exchange Rates
          </h2>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            Get the best exchange rates from multiple providers including Wise and ExchangeRate.host.
            Real-time data with server-side caching for optimal performance.
          </p>
        </div>

        {/* Currency Selector */}
        <CurrencySelector />

        {/* Features */}
        <div className="grid md:grid-cols-3 gap-8 mb-16">
          <div className="bg-white rounded-lg shadow-md p-6 text-center">
            <ArrowTrendingUpIcon className="h-12 w-12 text-blue-600 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Live Rates</h3>
            <p className="text-gray-600">Real-time exchange rates updated every minute from trusted providers.</p>
          </div>
          <div className="bg-white rounded-lg shadow-md p-6 text-center">
            <CurrencyDollarIcon className="h-12 w-12 text-green-600 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Best Rates</h3>
            <p className="text-gray-600">Automatically highlights the best rates to help you save money.</p>
          </div>
          <div className="bg-white rounded-lg shadow-md p-6 text-center">
            <GlobeAltIcon className="h-12 w-12 text-purple-600 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Multiple Providers</h3>
            <p className="text-gray-600">Compare rates from Wise, ExchangeRate.host, and more.</p>
          </div>
        </div>

        {/* Popular Currency Pairs */}
        <div className="bg-white rounded-lg shadow-md p-8">
          <h3 className="text-2xl font-bold text-gray-900 mb-6 text-center">Popular Currency Pairs</h3>
          <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-4">
            {popularPairs.map((pair) => (
              <Link
                key={`${pair.base}-${pair.quote}`}
                href={`/${pair.base}/${pair.quote}`}
                className="block p-4 border border-gray-200 rounded-lg hover:border-blue-500 hover:shadow-md transition-all duration-200"
              >
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-lg font-semibold text-gray-900">
                      {pair.base}/{pair.quote}
                    </div>
                    <div className="text-sm text-gray-600">{pair.name}</div>
                  </div>
                  <div className="text-blue-600">→</div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-white border-t border-gray-200 mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <p className="text-sm text-gray-500">
              Compare live exchange rates from multiple providers. Rates are for informational purposes only.
            </p>
            <p className="text-xs text-gray-400 mt-2">
              © 2024 FX Compare. Built with Next.js 14.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
