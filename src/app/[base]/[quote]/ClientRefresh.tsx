'use client';

import { useEffect } from 'react';
import { useRates } from '@/hooks/useRates';
import { ArrowPathIcon } from '@heroicons/react/24/outline';

interface ClientRefreshProps {
  base: string;
  quote: string;
}

export default function ClientRefresh({ base, quote }: ClientRefreshProps) {
  const { error, isLoading, mutate } = useRates(base, quote);

  // Auto-refresh every 60 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      mutate();
    }, 60000);

    return () => clearInterval(interval);
  }, [mutate]);

  const handleManualRefresh = () => {
    mutate();
  };

  return (
    <div className="mt-8 flex items-center justify-center">
      <button
        onClick={handleManualRefresh}
        disabled={isLoading}
        className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        <ArrowPathIcon 
          className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} 
        />
        {isLoading ? 'Refreshing...' : 'Refresh Rates'}
      </button>
      
      {error && (
        <div className="ml-4 text-sm text-red-600">
          Error: {error.message}
        </div>
      )}
    </div>
  );
}
