import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { getRates } from '@/lib/aggregate';
import { getProviderAttribution } from '@/lib/providerUtils';
import { getGhanaBankRates, isGhanaianCurrencyPair } from '@/lib/ghana/ghanaBankingAggregate';
import { Rate, GhanaBankRate } from '@/types';
import RateTable from '@/components/RateTable';
import ConversionCalc from '@/components/ConversionCalc';
import GhanaBankRateTable from '@/components/GhanaBankRateTable';
import GhanaCurrencyConverter from '@/components/GhanaCurrencyConverter';
import CurrencySelector from '@/components/CurrencySelector';

interface PageProps {
  params: Promise<{
    base: string;
    quote: string;
  }>;
}

// Common currency codes for validation
const VALID_CURRENCIES = [
  'USD', 'EUR', 'GBP', 'JPY', 'AUD', 'CAD', 'CHF', 'CNY', 'SEK', 'NZD',
  'MXN', 'SGD', 'HKD', 'NOK', 'TRY', 'ZAR', 'BRL', 'INR', 'KRW', 'PLN',
  'THB', 'IDR', 'HUF', 'CZK', 'ILS', 'CLP', 'PHP', 'AED', 'COP', 'SAR',
  'MYR', 'RON', 'BGN', 'HRK', 'RUB', 'DKK', 'ISK'
];

function isValidCurrency(code: string): boolean {
  return VALID_CURRENCIES.includes(code.toUpperCase());
}

export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const { base, quote } = await params;
  
  return {
    title: `${base.toUpperCase()}/${quote.toUpperCase()} Exchange Rate - FX Compare`,
    description: `Live exchange rates for ${base.toUpperCase()} to ${quote.toUpperCase()}. Compare rates from multiple providers including Wise and ExchangeRate.host.`,
    keywords: `${base}, ${quote}, exchange rate, currency conversion, forex, FX`,
  };
}

export default async function RatePage({ params }: PageProps) {
  const { base, quote } = await params;
  
  // Validate currency codes
  if (!isValidCurrency(base) || !isValidCurrency(quote)) {
    notFound();
  }

  // Prevent same currency comparison
  if (base.toUpperCase() === quote.toUpperCase()) {
    notFound();
  }

  let rates: Rate[] = [];
  let ghanaBankRates: GhanaBankRate[] = [];
  let error: string | undefined;
  const isGhanaPair = isGhanaianCurrencyPair(base, quote);

  try {
    // Fetch international exchange platform rates
    rates = await getRates(base, quote);

    // Fetch Ghana bank rates if this involves GHS
    if (isGhanaPair) {
      ghanaBankRates = await getGhanaBankRates(base, quote);
    }
  } catch (err) {
    console.error('Error fetching rates:', err);
    error = err instanceof Error ? err.message : 'Failed to fetch rates';
    rates = [];
    ghanaBankRates = [];
  }

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                FX Compare
              </h1>
              <p className="text-sm text-gray-600">
                Live exchange rate comparison
              </p>
            </div>
            <div className="text-right">
              <p className="text-sm text-gray-500">
                Last updated: {new Date().toLocaleString()}
              </p>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Currency Selector */}
        <CurrencySelector initialBase={base.toUpperCase()} initialQuote={quote.toUpperCase()} />
        {error ? (
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 mb-8">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">
                  Error Loading Rates
                </h3>
                <div className="mt-2 text-sm text-red-700">
                  <p>{error}</p>
                </div>
              </div>
            </div>
          </div>
        ) : null}

        <div className="space-y-8">
          {/* International Exchange Platforms */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Rate Table - Takes up 2 columns on large screens */}
            <div className="lg:col-span-2">
              <RateTable rates={rates} base={base.toUpperCase()} quote={quote.toUpperCase()} />
            </div>

            {/* Conversion Calculator - Takes up 1 column */}
            <div className="lg:col-span-1">
              <ConversionCalc rates={rates} base={base.toUpperCase()} quote={quote.toUpperCase()} />
            </div>
          </div>

          {/* Ghana Banking Section - Only show for GHS pairs */}
          {isGhanaPair && (
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Ghana Bank Rate Table - Takes up 2 columns */}
              <div className="lg:col-span-2">
                <GhanaBankRateTable
                  rates={ghanaBankRates}
                  baseCurrency={base.toUpperCase()}
                  quoteCurrency={quote.toUpperCase()}
                />
              </div>

              {/* Ghana Currency Converter - Takes up 1 column */}
              <div className="lg:col-span-1">
                <GhanaCurrencyConverter
                  rates={ghanaBankRates}
                  baseCurrency={base.toUpperCase()}
                  quoteCurrency={quote.toUpperCase()}
                />
              </div>
            </div>
          )}
        </div>

        {/* Note: Rates automatically refresh every 2 hours to conserve API usage */}
      </main>

      {/* Footer */}
      <footer className="bg-white border-t border-gray-200 mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <p className="text-sm text-gray-500">
              {getProviderAttribution(rates)}. Rates are for informational purposes only.
            </p>
            <p className="text-xs text-gray-400 mt-2">
              © 2024 FX Compare. Built with Next.js 14.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
