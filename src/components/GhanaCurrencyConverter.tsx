'use client';

import { useState, useEffect } from 'react';
import { GhanaBankRate } from '@/types';
import { getBestGhanaBankRate } from '@/lib/ghana/ghanaBankingAggregate';
import { CalculatorIcon, ArrowsRightLeftIcon, BuildingLibraryIcon } from '@heroicons/react/24/outline';
import ghanaBanksData from '@/data/ghanaBanks.json';

interface GhanaCurrencyConverterProps {
  rates: GhanaBankRate[];
  baseCurrency: string;
  quoteCurrency: string;
}

export default function GhanaCurrencyConverter({ rates, baseCurrency, quoteCurrency }: GhanaCurrencyConverterProps) {
  const [amount, setAmount] = useState<string>('1000');
  const [convertedAmount, setConvertedAmount] = useState<number>(0);
  const [isSellingForeignCurrency, setIsSellingForeignCurrency] = useState<boolean>(true);
  
  const bestRate = getBestGhanaBankRate(rates);
  const foreignCurrency = baseCurrency === 'GHS' ? quoteCurrency : baseCurrency;
  
  useEffect(() => {
    if (bestRate && amount) {
      const numAmount = parseFloat(amount);
      if (!isNaN(numAmount)) {
        // Determine conversion direction and rate to use
        let conversionRate: number;
        
        if (isSellingForeignCurrency) {
          // Selling foreign currency to get GHS (use bank's buying rate)
          conversionRate = bestRate.buyingRate;
        } else {
          // Buying foreign currency with GHS (use bank's selling rate)
          conversionRate = bestRate.sellingRate;
        }
        
        if (baseCurrency === 'GHS') {
          // Converting from GHS to foreign currency
          setConvertedAmount(numAmount / conversionRate);
        } else {
          // Converting from foreign currency to GHS
          setConvertedAmount(numAmount * conversionRate);
        }
      } else {
        setConvertedAmount(0);
      }
    } else {
      setConvertedAmount(0);
    }
  }, [amount, bestRate, baseCurrency, isSellingForeignCurrency]);

  const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    if (value === '' || /^\d*\.?\d*$/.test(value)) {
      setAmount(value);
    }
  };

  const handleSwapDirection = () => {
    setIsSellingForeignCurrency(!isSellingForeignCurrency);
  };

  if (!bestRate) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6 border-l-4 border-red-600">
        <div className="flex items-center mb-4">
          <span className="text-2xl mr-2">🇬🇭</span>
          <CalculatorIcon className="h-6 w-6 text-red-600 mr-2" />
          <h3 className="text-lg font-semibold text-gray-900">Ghana Currency Converter</h3>
        </div>
        <div className="text-center py-8">
          <p className="text-gray-500">No Ghana bank rates available for conversion.</p>
        </div>
      </div>
    );
  }

  const bank = ghanaBanksData[bestRate.bank as keyof typeof ghanaBanksData];
  const rateToUse = isSellingForeignCurrency ? bestRate.buyingRate : bestRate.sellingRate;
  const rateType = isSellingForeignCurrency ? 'Buying' : 'Selling';

  return (
    <div className="bg-white rounded-lg shadow-md p-6 border-l-4 border-red-600">
      <div className="flex items-center mb-6">
        <span className="text-2xl mr-2">🇬🇭</span>
        <CalculatorIcon className="h-6 w-6 text-red-600 mr-2" />
        <h3 className="text-lg font-semibold text-gray-900">Ghana Currency Converter</h3>
      </div>
      
      <div className="space-y-4">
        {/* Transaction Type Selector */}
        <div className="bg-red-50 rounded-lg p-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Transaction Type
          </label>
          <div className="flex space-x-4">
            <label className="flex items-center">
              <input
                type="radio"
                checked={isSellingForeignCurrency}
                onChange={() => setIsSellingForeignCurrency(true)}
                className="mr-2 text-red-600 focus:ring-red-500"
              />
              <span className="text-sm">Selling {foreignCurrency} (Getting GHS)</span>
            </label>
            <label className="flex items-center">
              <input
                type="radio"
                checked={!isSellingForeignCurrency}
                onChange={() => setIsSellingForeignCurrency(false)}
                className="mr-2 text-red-600 focus:ring-red-500"
              />
              <span className="text-sm">Buying {foreignCurrency} (Paying GHS)</span>
            </label>
          </div>
        </div>

        {/* Conversion Input */}
        <div className="flex items-center space-x-4">
          <div className="flex-1">
            <label htmlFor="amount" className="block text-sm font-medium text-gray-700 mb-1">
              {isSellingForeignCurrency ? `Amount in ${foreignCurrency}` : 'Amount in GHS'}
            </label>
            <div className="relative">
              <input
                type="text"
                id="amount"
                value={amount}
                onChange={handleAmountChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-red-500 focus:border-red-500 text-lg text-gray-900 bg-white"
                placeholder="Enter amount"
              />
              <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                <span className="text-gray-500 text-sm font-medium">
                  {isSellingForeignCurrency ? foreignCurrency : 'GHS'}
                </span>
              </div>
            </div>
          </div>
          
          <div className="flex-shrink-0 pt-6">
            <button
              onClick={handleSwapDirection}
              className="p-2 text-red-600 hover:text-red-700 transition-colors duration-200"
              title="Swap transaction direction"
            >
              <ArrowsRightLeftIcon className="h-5 w-5" />
            </button>
          </div>
          
          <div className="flex-1">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {isSellingForeignCurrency ? 'You will receive (GHS)' : `You will receive (${foreignCurrency})`}
            </label>
            <div className="relative">
              <div className="block w-full px-3 py-2 border border-gray-300 rounded-md bg-red-50 text-lg font-semibold text-red-600">
                {amount && !isNaN(parseFloat(amount)) 
                  ? convertedAmount.toFixed(4)
                  : '0.0000'
                }
              </div>
              <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                <span className="text-gray-500 text-sm font-medium">
                  {isSellingForeignCurrency ? 'GHS' : foreignCurrency}
                </span>
              </div>
            </div>
          </div>
        </div>
        
        {/* Rate Information */}
        <div className="bg-red-50 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Bank {rateType} Rate</p>
              <p className="text-lg font-semibold text-gray-900">
                1 {foreignCurrency} = {rateToUse.toFixed(4)} GHS
              </p>
            </div>
            <div className="text-right">
              <p className="text-sm text-gray-600">Best Rate From</p>
              <div className="flex items-center">
                <BuildingLibraryIcon className="h-4 w-4 text-red-600 mr-1" />
                <p className="text-sm font-medium text-red-600">
                  {bank?.name || bestRate.bank}
                </p>
              </div>
            </div>
          </div>
        </div>
        
        {/* Quick Amount Buttons */}
        <div className="flex flex-wrap gap-2">
          <span className="text-sm text-gray-600 self-center">Quick amounts:</span>
          {['100', '500', '1000', '5000', '10000'].map((quickAmount) => (
            <button
              key={quickAmount}
              onClick={() => setAmount(quickAmount)}
              className="px-3 py-1 text-sm bg-red-100 hover:bg-red-200 text-red-800 font-medium rounded-md transition-colors duration-200 border border-red-300"
            >
              {quickAmount}
            </button>
          ))}
        </div>
        
        {/* Important Disclaimers */}
        <div className="text-xs text-gray-500 pt-2 border-t border-gray-200 space-y-1">
          <p className="flex items-center">
            <span className="text-lg mr-1">⚠️</span>
            <strong>Important:</strong> These are indicative rates only.
          </p>
          <p>• Contact the bank directly for official rates and to complete transactions</p>
          <p>• Actual rates may vary based on transaction amount and bank policies</p>
          <p>• Additional fees and charges may apply</p>
          <p>• Rate updated: {new Date(bestRate.lastUpdated).toLocaleString()}</p>
        </div>
      </div>
    </div>
  );
}
