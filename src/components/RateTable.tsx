'use client';

import { Rate } from '@/types';
import { formatRate } from '@/lib/aggregate';
import { getShortProviderAttribution } from '@/lib/providerUtils';
import { CheckCircleIcon } from '@heroicons/react/24/solid';
import providersData from '@/data/providers.json';

interface RateTableProps {
  rates: Rate[];
  base: string;
  quote: string;
}

export default function RateTable({ rates, base, quote }: RateTableProps) {
  if (rates.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">
          Exchange Rates: {base}/{quote}
        </h2>
        <div className="text-center py-8">
          <p className="text-gray-500">No rates available at the moment.</p>
          <p className="text-sm text-gray-400 mt-2">Please try again later.</p>
        </div>
      </div>
    );
  }

  // Find the best rate (highest buy rate)
  const bestRate = rates[0]; // Already sorted by highest buy rate

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden">
      <div className="px-6 py-4 border-b border-gray-200">
        <h2 className="text-xl font-semibold text-gray-900">
          Exchange Rates: {base}/{quote}
        </h2>
        <p className="text-sm text-gray-600 mt-1">
          Showing rates from {rates.length} provider{rates.length !== 1 ? 's' : ''}
        </p>
      </div>
      
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Provider
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Buy Rate
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Sell Rate
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Spread
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Last Updated
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {rates.map((rate, index) => {
              const isBestRate = rate === bestRate;
              const provider = providersData[rate.provider as keyof typeof providersData];
              const spread = rate.spread || (rate.sellRate - rate.buyRate);
              
              return (
                <tr 
                  key={`${rate.provider}-${index}`}
                  className={`${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'} ${
                    isBestRate ? 'ring-2 ring-green-500 ring-inset' : ''
                  }`}
                >
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      {isBestRate && (
                        <CheckCircleIcon className="h-5 w-5 text-green-500 mr-2" />
                      )}
                      <div>
                        <div className={`text-sm font-medium ${
                          isBestRate ? 'text-green-600' : 'text-gray-900'
                        }`}>
                          {provider?.name || rate.provider}
                        </div>
                        {isBestRate && (
                          <div className="text-xs text-green-600 font-medium">
                            Best Rate
                          </div>
                        )}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className={`text-sm ${
                      isBestRate ? 'text-green-600 font-semibold' : 'text-gray-900'
                    }`}>
                      {formatRate(rate.buyRate)}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className={`text-sm ${
                      isBestRate ? 'text-green-600 font-semibold' : 'text-gray-900'
                    }`}>
                      {formatRate(rate.sellRate)}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      {formatRate(spread)}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-500">
                      {new Date(rate.timestamp).toLocaleTimeString()}
                    </div>
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
      
      <div className="px-6 py-3 bg-gray-50 border-t border-gray-200">
        <p className="text-xs text-gray-500">
          {getShortProviderAttribution(rates)}. Informational only.
        </p>
      </div>
    </div>
  );
}
