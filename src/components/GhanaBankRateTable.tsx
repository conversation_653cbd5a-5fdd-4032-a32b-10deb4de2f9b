'use client';

import { GhanaBankRate } from '@/types';
import { CheckCircleIcon, BuildingLibraryIcon } from '@heroicons/react/24/solid';
import ghanaBanksData from '@/data/ghanaBanks.json';

interface GhanaBankRateTableProps {
  rates: GhanaBankRate[];
  baseCurrency: string;
  quoteCurrency: string;
}

export default function GhanaBankRateTable({ rates, baseCurrency, quoteCurrency }: GhanaBankRateTableProps) {
  if (rates.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center mb-4">
          <div className="flex items-center">
            <span className="text-2xl mr-2">🇬🇭</span>
            <BuildingLibraryIcon className="h-6 w-6 text-red-600 mr-2" />
            <h2 className="text-xl font-semibold text-gray-900">
              Ghana Bank Rates: {baseCurrency}/{quoteCurrency}
            </h2>
          </div>
        </div>
        <div className="text-center py-8">
          <p className="text-gray-500">No Ghana bank rates available at the moment.</p>
          <p className="text-sm text-gray-400 mt-2">
            {baseCurrency === 'GHS' || quoteCurrency === 'GHS' 
              ? 'Please try again later or check bank websites directly.'
              : 'This section only shows rates for currency pairs involving Ghana Cedi (GHS).'
            }
          </p>
        </div>
      </div>
    );
  }

  // Find the best rate (highest buying rate)
  const bestRate = rates[0]; // Already sorted by best buying rate

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden border-l-4 border-red-600">
      <div className="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-red-50 to-yellow-50">
        <div className="flex items-center">
          <span className="text-2xl mr-3">🇬🇭</span>
          <div>
            <h2 className="text-xl font-semibold text-gray-900 flex items-center">
              <BuildingLibraryIcon className="h-6 w-6 text-red-600 mr-2" />
              Ghana Bank Rates: {baseCurrency}/{quoteCurrency}
            </h2>
            <p className="text-sm text-gray-600 mt-1">
              Live rates from {rates.length} Ghanaian bank{rates.length !== 1 ? 's' : ''}
            </p>
          </div>
        </div>
      </div>
      
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Bank
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Buying Rate
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Selling Rate
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Spread
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Last Updated
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {rates.map((rate, index) => {
              const isBestRate = rate === bestRate;
              const bank = ghanaBanksData[rate.bank as keyof typeof ghanaBanksData];
              const spread = rate.sellingRate - rate.buyingRate;
              
              return (
                <tr 
                  key={`${rate.bank}-${rate.currency}-${index}`}
                  className={`${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'} ${
                    isBestRate ? 'ring-2 ring-green-500 ring-inset' : ''
                  }`}
                >
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      {isBestRate && (
                        <CheckCircleIcon className="h-5 w-5 text-green-500 mr-2" />
                      )}
                      <div className="flex items-center">
                        <div className="h-8 w-8 rounded mr-3 bg-red-100 flex items-center justify-center">
                          <BuildingLibraryIcon className="h-5 w-5 text-red-600" />
                        </div>
                        <div>
                          <div className={`text-sm font-medium ${
                            isBestRate ? 'text-green-600' : 'text-gray-900'
                          }`}>
                            {bank?.name || rate.bank}
                          </div>
                          {isBestRate && (
                            <div className="text-xs text-green-600 font-medium">
                              Best Rate
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className={`text-sm ${
                      isBestRate ? 'text-green-600 font-semibold' : 'text-gray-900'
                    }`}>
                      {rate.buyingRate.toFixed(4)}
                    </div>
                    <div className="text-xs text-gray-500">
                      GHS per {rate.currency}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className={`text-sm ${
                      isBestRate ? 'text-green-600 font-semibold' : 'text-gray-900'
                    }`}>
                      {rate.sellingRate.toFixed(4)}
                    </div>
                    <div className="text-xs text-gray-500">
                      GHS per {rate.currency}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      {spread.toFixed(4)}
                    </div>
                    <div className="text-xs text-gray-500">
                      {((spread / rate.buyingRate) * 100).toFixed(2)}%
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-500">
                      {new Date(rate.lastUpdated).toLocaleTimeString()}
                    </div>
                    <div className="text-xs text-gray-400">
                      {rate.source === 'web-scraping' ? 'Scraped' : 'API'}
                    </div>
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
      
      <div className="px-6 py-3 bg-red-50 border-t border-gray-200">
        <div className="flex items-center text-xs text-gray-600">
          <span className="text-lg mr-2">⚠️</span>
          <div>
            <p className="font-medium">Important Disclaimer:</p>
            <p>
              Bank rates are indicative and may differ from actual transaction rates. 
              Contact banks directly for official rates and to complete transactions.
              Rates are updated periodically and may not reflect real-time changes.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
