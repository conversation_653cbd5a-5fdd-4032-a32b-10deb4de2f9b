'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { ChevronDownIcon, ArrowsRightLeftIcon } from '@heroicons/react/24/outline';

// Popular currencies for the selector - GHS prominently featured
const CURRENCIES = [
  { code: 'GHS', name: 'Ghana Cedi', flag: '🇬🇭', featured: true },
  { code: 'USD', name: 'US Dollar', flag: '🇺🇸', featured: true },
  { code: 'EUR', name: 'Euro', flag: '🇪🇺', featured: true },
  { code: 'GBP', name: 'British Pound', flag: '🇬🇧', featured: true },
  { code: 'JPY', name: 'Japanese Yen', flag: '🇯🇵' },
  { code: 'AUD', name: 'Australian Dollar', flag: '🇦🇺' },
  { code: 'CAD', name: 'Canadian Dollar', flag: '🇨🇦' },
  { code: 'CHF', name: 'Swiss Franc', flag: '🇨🇭' },
  { code: 'CNY', name: 'Chinese Yuan', flag: '🇨🇳' },
  { code: 'SEK', name: 'Swedish Krona', flag: '🇸🇪' },
  { code: 'NZD', name: 'New Zealand Dollar', flag: '🇳🇿' },
  { code: 'SGD', name: 'Singapore Dollar', flag: '🇸🇬' },
  { code: 'HKD', name: 'Hong Kong Dollar', flag: '🇭🇰' },
  { code: 'NOK', name: 'Norwegian Krone', flag: '🇳🇴' },
  { code: 'MXN', name: 'Mexican Peso', flag: '🇲🇽' },
  { code: 'INR', name: 'Indian Rupee', flag: '🇮🇳' },
  { code: 'KRW', name: 'South Korean Won', flag: '🇰🇷' },
  { code: 'BRL', name: 'Brazilian Real', flag: '🇧🇷' },
  { code: 'ZAR', name: 'South African Rand', flag: '🇿🇦' },
];

interface CurrencySelectorProps {
  initialBase?: string;
  initialQuote?: string;
}

export default function CurrencySelector({ initialBase = 'USD', initialQuote = 'EUR' }: CurrencySelectorProps) {
  const [baseCurrency, setBaseCurrency] = useState(initialBase);
  const [quoteCurrency, setQuoteCurrency] = useState(initialQuote);
  const router = useRouter();

  const handleSwapCurrencies = () => {
    const newBase = quoteCurrency;
    const newQuote = baseCurrency;
    setBaseCurrency(newBase);
    setQuoteCurrency(newQuote);
  };

  const handleCompare = () => {
    if (baseCurrency && quoteCurrency && baseCurrency !== quoteCurrency) {
      router.push(`/${baseCurrency}/${quoteCurrency}`);
    }
  };

  const handleBaseCurrencyChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newBase = e.target.value;
    setBaseCurrency(newBase);
    if (newBase === quoteCurrency) {
      // If base becomes same as quote, swap quote to something else
      const otherCurrency = CURRENCIES.find(c => c.code !== newBase)?.code || 'EUR';
      setQuoteCurrency(otherCurrency);
    }
  };

  const handleQuoteCurrencyChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newQuote = e.target.value;
    setQuoteCurrency(newQuote);
    if (newQuote === baseCurrency) {
      // If quote becomes same as base, swap base to something else
      const otherCurrency = CURRENCIES.find(c => c.code !== newQuote)?.code || 'USD';
      setBaseCurrency(otherCurrency);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6 mb-8">
      <h3 className="text-lg font-semibold text-gray-900 mb-4 text-center">
        Compare Exchange Rates
      </h3>
      
      <div className="flex items-center space-x-4">
        {/* Base Currency Selector */}
        <div className="flex-1">
          <label htmlFor="base-currency" className="block text-sm font-medium text-gray-700 mb-2">
            From
          </label>
          <div className="relative">
            <select
              id="base-currency"
              value={baseCurrency}
              onChange={handleBaseCurrencyChange}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-gray-900 bg-white appearance-none"
            >
              {CURRENCIES.map((currency) => (
                <option
                  key={currency.code}
                  value={currency.code}
                  className={currency.featured ? 'font-semibold' : ''}
                >
                  {currency.flag} {currency.code} - {currency.name}
                  {currency.code === 'GHS' ? ' (Ghana Banks Available)' : ''}
                </option>
              ))}
            </select>
            <ChevronDownIcon className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
          </div>
        </div>

        {/* Swap Button */}
        <div className="flex-shrink-0 pt-6">
          <button
            onClick={handleSwapCurrencies}
            className="p-2 text-gray-400 hover:text-gray-600 transition-colors duration-200"
            title="Swap currencies"
          >
            <ArrowsRightLeftIcon className="h-5 w-5" />
          </button>
        </div>

        {/* Quote Currency Selector */}
        <div className="flex-1">
          <label htmlFor="quote-currency" className="block text-sm font-medium text-gray-700 mb-2">
            To
          </label>
          <div className="relative">
            <select
              id="quote-currency"
              value={quoteCurrency}
              onChange={handleQuoteCurrencyChange}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-gray-900 bg-white appearance-none"
            >
              {CURRENCIES.map((currency) => (
                <option
                  key={currency.code}
                  value={currency.code}
                  className={currency.featured ? 'font-semibold' : ''}
                >
                  {currency.flag} {currency.code} - {currency.name}
                  {currency.code === 'GHS' ? ' (Ghana Banks Available)' : ''}
                </option>
              ))}
            </select>
            <ChevronDownIcon className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
          </div>
        </div>

        {/* Compare Button */}
        <div className="flex-shrink-0 pt-6">
          <button
            onClick={handleCompare}
            disabled={!baseCurrency || !quoteCurrency || baseCurrency === quoteCurrency}
            className="px-6 py-2 bg-blue-600 text-white font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
          >
            Compare
          </button>
        </div>
      </div>

      <div className="mt-4 text-center">
        <p className="text-sm text-gray-600">
          Select currencies to compare live exchange rates from multiple providers
        </p>
      </div>
    </div>
  );
}
