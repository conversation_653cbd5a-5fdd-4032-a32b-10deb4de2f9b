'use client';

import { useState, useEffect } from 'react';
import { Rate } from '@/types';
import { getBestRate, formatCurrency, formatRate } from '@/lib/aggregate';
import { ArrowRightIcon, CalculatorIcon } from '@heroicons/react/24/outline';
import providersData from '@/data/providers.json';

interface ConversionCalcProps {
  rates: Rate[];
  base: string;
  quote: string;
}

export default function ConversionCalc({ rates, base, quote }: ConversionCalcProps) {
  const [amount, setAmount] = useState<string>('1000');
  const [convertedAmount, setConvertedAmount] = useState<number>(0);
  
  const bestRate = getBestRate(rates);
  
  useEffect(() => {
    if (bestRate && amount) {
      const numAmount = parseFloat(amount);
      if (!isNaN(numAmount)) {
        setConvertedAmount(numAmount * bestRate.buyRate);
      } else {
        setConvertedAmount(0);
      }
    } else {
      setConvertedAmount(0);
    }
  }, [amount, bestRate]);

  const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    // Allow empty string, numbers, and decimal points
    if (value === '' || /^\d*\.?\d*$/.test(value)) {
      setAmount(value);
    }
  };

  if (!bestRate) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center mb-4">
          <CalculatorIcon className="h-6 w-6 text-gray-400 mr-2" />
          <h3 className="text-lg font-semibold text-gray-900">Currency Converter</h3>
        </div>
        <div className="text-center py-8">
          <p className="text-gray-500">No rates available for conversion.</p>
        </div>
      </div>
    );
  }

  const provider = providersData[bestRate.provider as keyof typeof providersData];

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex items-center mb-6">
        <CalculatorIcon className="h-6 w-6 text-blue-500 mr-2" />
        <h3 className="text-lg font-semibold text-gray-900">Currency Converter</h3>
      </div>
      
      <div className="space-y-4">
        {/* Input Section */}
        <div className="flex items-center space-x-4">
          <div className="flex-1">
            <label htmlFor="amount" className="block text-sm font-medium text-gray-700 mb-1">
              Amount in {base}
            </label>
            <div className="relative">
              <input
                type="text"
                id="amount"
                value={amount}
                onChange={handleAmountChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-lg text-gray-900 bg-white"
                placeholder="Enter amount"
              />
              <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                <span className="text-gray-500 text-sm font-medium">{base}</span>
              </div>
            </div>
          </div>
          
          <div className="flex-shrink-0 pt-6">
            <ArrowRightIcon className="h-6 w-6 text-gray-400" />
          </div>
          
          <div className="flex-1">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Converted Amount
            </label>
            <div className="relative">
              <div className="block w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-lg font-semibold text-green-600">
                {amount && !isNaN(parseFloat(amount)) 
                  ? formatCurrency(convertedAmount, quote).replace(/[^\d.,]/g, '')
                  : '0.00'
                }
              </div>
              <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                <span className="text-gray-500 text-sm font-medium">{quote}</span>
              </div>
            </div>
          </div>
        </div>
        
        {/* Rate Information */}
        <div className="bg-gray-50 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Exchange Rate</p>
              <p className="text-lg font-semibold text-gray-900">
                1 {base} = {formatRate(bestRate.buyRate)} {quote}
              </p>
            </div>
            <div className="text-right">
              <p className="text-sm text-gray-600">Best Rate From</p>
              <p className="text-sm font-medium text-green-600">
                {provider?.name || bestRate.provider}
              </p>
            </div>
          </div>
        </div>
        
        {/* Quick Amount Buttons */}
        <div className="flex space-x-2">
          <span className="text-sm text-gray-600 self-center">Quick amounts:</span>
          {['100', '500', '1000', '5000'].map((quickAmount) => (
            <button
              key={quickAmount}
              onClick={() => setAmount(quickAmount)}
              className="px-3 py-1 text-sm bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium rounded-md transition-colors duration-200 border border-gray-300"
            >
              {quickAmount}
            </button>
          ))}
        </div>
        
        {/* Disclaimer */}
        <div className="text-xs text-gray-500 pt-2 border-t border-gray-200">
          <p>
            Rate updated: {new Date(bestRate.timestamp).toLocaleString()}
          </p>
          <p className="mt-1">
            Rates are indicative and may differ from actual transaction rates.
          </p>
        </div>
      </div>
    </div>
  );
}
